<!-- ============================================ -->
<!--                   Banner                     -->
<!-- ============================================ -->

<div id="banner-1014">
    <div class="cs-container">
        <picture class="cs-picture">
            <img class="cs-icon" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Ffancy-divider.svg" alt="divider" width="181" height="36" aria-hidden="true">
        </picture>
        <span class="cs-int-title">About Pureluxus</span>
        <p class="cs-text">
            Where science meets luxury to unveil your most radiant self.
        </p>
    </div>
    <!--Background Image-->
    <picture class="cs-background">
        <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80">
        <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80">
        <img decoding="async" src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80" alt="luxury cosmetics and skincare products" width="1920" height="1000">
    </picture>
</div>   

<!-- ============================================ -->
<!--                 Side By Side                 -->
<!-- ============================================ -->

<section id="sbs-1014">
    <div class="cs-container">
        <!-- Left Image Section -->
        <div class="cs-image-group">
            <picture class="cs-picture cs-picture1">
                <source media="(max-width: 600px)" srcset="https://images.pexels.com/photos/7796170/pexels-photo-7796170.jpeg?auto=compress&cs=tinysrgb&w=600&h=581&fit=crop">
                <source media="(min-width: 601px)" srcset="https://images.pexels.com/photos/7796170/pexels-photo-7796170.jpeg?auto=compress&cs=tinysrgb&w=522&h=581&fit=crop">
                <img loading="lazy" decoding="async" src="https://images.pexels.com/photos/7796170/pexels-photo-7796170.jpeg?auto=compress&cs=tinysrgb&w=522&h=581&fit=crop" alt="aesthetic arrangement of natural skincare products and accessories" width="522" height="581" aria-hidden="true">
            </picture>
            <picture class="cs-picture cs-picture2">
                <source media="(max-width: 600px)" srcset="https://images.pexels.com/photos/8101520/pexels-photo-8101520.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop">
                <source media="(min-width: 601px)" srcset="https://images.pexels.com/photos/8101520/pexels-photo-8101520.jpeg?auto=compress&cs=tinysrgb&w=414&h=400&fit=crop">
                <img loading="lazy" decoding="async" src="https://images.pexels.com/photos/8101520/pexels-photo-8101520.jpeg?auto=compress&cs=tinysrgb&w=414&h=400&fit=crop" alt="flat lay of luxury skincare products in jars and dropper bottles" width="414" height="400" aria-hidden="true">
            </picture>
        </div>
        <!-- Right Content Section-->
        <div class="cs-content">
            <span class="cs-topper">About Us</span>
            <h2 class="cs-title">Redefining Luxury Skincare</h2>
            <p class="cs-text">
                At Pureluxus, we believe that exceptional beauty begins with exceptional ingredients. Founded on the principle that luxury and efficacy are inseparable, we've dedicated ourselves to creating premium cosmetics that transform your skincare routine into a ritual of self-care and indulgence. Our journey started with a simple yet powerful vision: to harness the finest botanical treasures and advanced scientific innovation to deliver visible, lasting results.
            </p>
            <p class="cs-text">
                Every Pureluxus formulation is a masterpiece of precision and purity. We meticulously source rare, ethically-harvested ingredients from pristine environments around the globe—from Swiss alpine botanicals to precious Japanese camellia oil. Our expert chemists blend these treasures with cutting-edge peptides, vitamins, and antioxidants to create products that don't just promise beauty, but deliver it. Each texture, each scent, each application is designed to awaken your senses while nourishing your skin at the deepest level.
            </p>
            <div class="cs-quote">
                <span class="cs-quote-text">
                    True luxury is not just what you see in the mirror—it's what you feel in your soul. Confidence, radiance, and timeless beauty come from within, and that's what Pureluxus awakens in every woman.
                </span>
                <span class="cs-name">Sophia Beaumont</span>
                <span class="cs-job">Founder & Chief Formulation Officer</span>
                <img class="cs-quote-icon" loading="lazy" decoding="async" src="https://csimg.nyc3.digitaloceanspaces.com/SideBySide/quote-white.svg" alt="gavel" width="136" height="77">
            </div>
        </div>
    </div>
</section>
          
<!-- ============================================ -->
<!--                    CTA                       -->
<!-- ============================================ -->

<section id="cta-1014">
    <div class="cs-container">
        <div class="cs-left-section">
            <h2 class="cs-title">Experience Pureluxus</h2>
            <a href="/contactsection" class="cs-button-solid">Get In Touch</a>
        </div>
        <div class="cs-content">
            <span class="cs-header">The Pureluxus Promise</span>
            <p class="cs-p">
                100% Cruelty-Free & Vegan<br>
                Sustainably Sourced Ingredients
            </p>
            <p class="cs-p">
                Clinically Tested & Dermatologist Approved<br>
                Luxe Formulations for Every Skin Type
            </p>
        </div>
        <picture class="cs-background">
            <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1556228578-8c89e6adf883?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
            <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1556228578-8c89e6adf883?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80">
            <img aria-hidden="true" loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1556228578-8c89e6adf883?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80" alt="premium cosmetics and beauty products" width="275" height="132">
        </picture>
    </div>
</section>

<style>
    /*-- -------------------------- -->
<---          Banner            -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0em) {
  #banner-1014 {
    padding: 0 1rem;
    /* 160px - 245px */
    padding-top: clamp(10rem, 25vw, 15.3125rem);
    padding-bottom: 7.5rem;
    background-color: #000;
    position: relative;
    z-index: 1;
  }
  #banner-1014 .cs-container {
    text-align: center;
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  #banner-1014 .cs-picture {
    width: 100%;
    margin: 0 0 0.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    position: relative;
  }
  #banner-1014 .cs-picture:before {
    /* left line */
    content: "";
    width: 50%;
    max-width: 9.375rem;
    height: 1px;
    background: #b4b2c7;
    opacity: 1;
    position: relative;
    display: block;
  }
  #banner-1014 .cs-picture:after {
    /* right line */
    content: "";
    width: 50%;
    max-width: 9.375rem;
    height: 1px;
    background: #b4b2c7;
    opacity: 1;
    position: relative;
    display: block;
  }
  #banner-1014 .cs-icon {
    width: 40%;
    /* 100px - 180px */
    max-width: clamp(6.25rem, 18vw, 11.25rem);
    height: auto;
    /* prevents flexbox from squishing it */
    flex: none;
  }
  #banner-1014 .cs-int-title {
    /* 39px - 61px */
    font-size: clamp(2.4375rem, 6.5vw, 3.8125rem);
    font-weight: 900;
    line-height: 1.2em;
    text-align: center;
    max-width: 43.75rem;
    margin: 0 0 1rem 0;
    color: var(--bodyTextColorWhite);
    position: relative;
  }
  #banner-1014 .cs-text {
    color: var(--bodyTextColorWhite);
  }
  #banner-1014 .cs-background {
    width: 100%;
    height: 100%;
    opacity: 0.7;
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    z-index: -1;
  }
  #banner-1014 .cs-background:before {
    /* black overlay box */
    content: "";
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.72;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    z-index: 1;
  }
  #banner-1014 .cs-background img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}
/*-- -------------------------- -->
<---       Side By Side         -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #sbs-1014 {
    padding: var(--sectionPadding);
    background-color: #FFF5F3; /* Very light peach background */
  }
  #sbs-1014 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 6vw, 4rem);
  }
  #sbs-1014 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: left;
    width: 100%;
    max-width: 33.875rem;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: flex-start;
  }
  #sbs-1014 .cs-text {
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    text-align: inherit;
    width: 100%;
    max-width: 40.625rem;
    margin: 0;
    color: var(--bodyTextColor);
  }
  #sbs-1014 .cs-text {
    margin-bottom: 1rem;
  }
  #sbs-1014 .cs-text:last-of-type {
    margin-bottom: 2rem;
  }
  #sbs-1014 .cs-quote {
    margin: 0 0 2rem 0;
    /* 16px - 32px */
    padding: clamp(1rem, 3vw, 2rem);
    background-color: var(--headerColor); /* Dark brown */
    position: relative;
  }
  #sbs-1014 .cs-quote-text {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.5em;
    margin: 0 0 1rem;
    color: var(--bodyTextColorWhite);
    display: block;
  }
  #sbs-1014 .cs-name {
    font-size: 1rem;
    line-height: 1.2em;
    text-transform: uppercase;
    font-weight: bold;
    margin: 0 0 0.25rem;
    color: var(--primary);
    display: block;
  }
  #sbs-1014 .cs-job {
    font-size: 0.875rem;
    line-height: 1.5em;
    color: var(--bodyTextColorWhite);
    display: block;
  }
  #sbs-1014 .cs-quote-icon {
    /* 60px - 136px */
    width: clamp(3.75rem, 10vw, 8.5rem);
    height: auto;
    position: absolute;
    bottom: 0rem;
    /* 16px - 32px */
    right: clamp(1rem, 4vw, 2rem);
  }
  #sbs-1014 .cs-image-group {
    /* scaling the font size with the view width */
    font-size: min(2.31vw, .7em);
    /* using ems so we can use font size to scale the whole section */
    width: 39.4375em;
    height: 39.75em;
    position: relative;
  }
  #sbs-1014 .cs-picture {
    position: absolute;
    display: block;
  }
  #sbs-1014 .cs-picture img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    /* makes image act like a background image */
    object-fit: cover;
  }
  #sbs-1014 .cs-picture1 {
    width: 32.625em;
    height: 36.3125em;
    left: 0;
    top: 0;
  }
  #sbs-1014 .cs-picture2 {
    width: 25.875em;
    height: 25em;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 40px;
    /* 6px - 12px */
    border: clamp(0.375em, 1.5vw, 0.75em) solid #fff;
    right: 0;
    bottom: 0;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #sbs-1014 .cs-container {
    flex-flow: row;
    justify-content: space-between;
    gap: 3.25rem;
  }
  #sbs-1014 .cs-image-group {
    font-size: min(1.2vw, 1em);
    flex: none;
  }
  #sbs-1014 .cs-content {
    margin: 0;
  }
}
/*-- -------------------------- -->
<---            CTA             -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #cta-1014 {
    padding: var(--sectionPadding);
  }
  #cta-1014 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    /* 32px - 88px top & bottom */
    /* 24px - 88px left & right */
    padding: clamp(2em, 6.3vw, 5.5em) clamp(1.5em, 5.7vw, 5.5em);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 2rem;
    position: relative;
    /* clips the corners for the border radius to show */
    overflow: hidden;
    z-index: 1;
    /* prevents padding from adding to height and width */
    box-sizing: border-box;
  }
  #cta-1014 .cs-background {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    z-index: -1;
  }
  #cta-1014 .cs-background:before {
    /* black overlay */
    content: "";
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.8;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    z-index: 1;
  }
  #cta-1014 .cs-background img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
  #cta-1014 .cs-left-section {
    max-width: 27.125rem;
  }
  #cta-1014 .cs-title {
    /* 25px - 49px */
    font-size: clamp(1.5625rem, 3.9vw, 3.0625rem);
    font-weight: 900;
    line-height: 1.2em;
    text-align: left;
    max-width: 50rem;
    /* 20px - 48px */
    margin: 0 auto clamp(1.25rem, 4.7vw, 3rem);
    color: var(--bodyTextColorWhite);
    position: relative;
  }
  #cta-1014 .cs-button-solid {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875em, 5.5vw, 3.5em);
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: auto;
    color: #fff;
    min-width: 9.375rem;
    padding: 0 2rem;
    background-color: var(--primary);
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
  }
  #cta-1014 .cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #E67E5B; /* Darker peach for hover */
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #cta-1014 .cs-button-solid:hover:before {
    width: 100%;
  }
  #cta-1014 .cs-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    /* 12px - 20px */
    gap: clamp(0.75rem, 1.6vw, 1.25rem);
  }
  #cta-1014 .cs-header {
    /* 20px - 25px */
    font-size: clamp(1.25rem, 2.4vw, 1.5625rem);
    font-weight: bold;
    color: var(--primary);
    display: block;
  }
  #cta-1014 .cs-p {
    /* 14px - 20px */
    font-size: clamp(0.875rem, 1.5vw, 1.25rem);
    line-height: 1.5em;
    margin: 0;
    color: var(--bodyTextColorWhite);
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #cta-1014 .cs-container {
    flex-direction: row;
    justify-content: space-between;
  }
  #cta-1014 .cs-content {
    align-items: flex-end;
    text-align: right;
    /* prevents flexbox from squishing it */
    flex: none;
  }
}
                                
</style>