<!-- ============================================ -->
<!--                 Why Choose Us                -->
<!-- ============================================ -->

<section id="why-choose-682">
    <div class="cs-container">
        <!-- Left Section -->
        <div class="cs-image-group">
            <picture class="cs-picture cs-picture1">
                <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=306&h=360&fit=crop&crop=center">
                <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=306&h=360&fit=crop&crop=center">
                <img loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=306&h=360&fit=crop&crop=center" alt="luxury skincare serum bottles with elegant gold accents" width="306" height="360">
            </picture>
            <picture class="cs-picture cs-picture2">
                <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=306&h=360&fit=crop&crop=center">
                <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=306&h=360&fit=crop&crop=center">
                <img loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=306&h=360&fit=crop&crop=center" alt="premium cosmetic bottles and beauty products arrangement" width="306" height="360">
            </picture>
            <picture class="cs-picture cs-picture3">
                <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=306&h=360&fit=crop&crop=center">
                <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=306&h=360&fit=crop&crop=center">
                <img loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=306&h=360&fit=crop&crop=center" alt="luxury beauty products and cosmetics arrangement" width="306" height="360">
            </picture>
            <picture class="cs-picture cs-picture4">
                <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=306&h=360&fit=crop&crop=center">
                <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=306&h=360&fit=crop&crop=center">
                <img loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=306&h=360&fit=crop&crop=center" alt="premium skincare and beauty products" width="306" height="360">
            </picture>
            <!--Zig Zag Graphic-->
            <img class="cs-graphic" aria-hidden="true" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FGraphics%2Fzigzag.svg" loading="lazy" decoding="async" alt="zigzag" width="983" height="416">
        </div>
        <div class="cs-content">
            <span class="cs-topper">Why Choose Us</span>
            <h2 class="cs-title">Elevate Your <span class="cs-color">Beauty Ritual</span> With Pureluxus</h2>
            <p class="cs-text">
                Discover the transformative power of luxury skincare crafted with precision, passion, and the finest ingredients nature and science can offer:
            </p>
            <ul class="cs-ul">
                <li class="cs-li">
                    <h3 class="cs-h3">
                        <img class="cs-icon" aria-hidden="true" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fsquares.svg" loading="lazy" decoding="async" alt="squares" width="17" height="18">
                        Clinically-Proven Formulations
                    </h3>
                    <p class="cs-item-text">
                        Our advanced formulations combine rare botanical extracts with cutting-edge peptides, vitamins, and antioxidants. Each ingredient is meticulously selected and clinically tested to deliver visible, lasting results. From Swiss alpine rose to Japanese camellia oil, we source only the most potent, ethically-harvested ingredients from pristine environments worldwide.
                    </p>
                </li>
                <li class="cs-li">
                    <h3 class="cs-h3">
                        <img class="cs-icon" aria-hidden="true" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fsquares.svg" loading="lazy" decoding="async" alt="squares" width="17" height="18">
                        Sensorial Excellence
                    </h3>
                    <p class="cs-item-text">
                        Every Pureluxus product is a masterpiece of texture, scent, and performance. Our expert perfumers and cosmetic chemists craft luxurious formulations that transform your daily skincare into an indulgent spa-like ritual. Each application awakens your senses while delivering transformative benefits to your skin.
                    </p>
                </li>
                <li class="cs-li">
                    <h3 class="cs-h3">
                        <img class="cs-icon" aria-hidden="true" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fsquares.svg" loading="lazy" decoding="async" alt="squares" width="17" height="18">
                        Ethical Luxury
                    </h3>
                    <p class="cs-item-text">
                        Beauty should never come at a cost to our planet or its creatures. Pureluxus is 100% cruelty-free, vegan, and committed to sustainable practices. Our eco-conscious packaging and responsibly-sourced ingredients mean you can indulge guilt-free. Dermatologist-approved and suitable for all skin types, including sensitive skin.
                    </p>
                </li>
            </ul>
            <a class="cs-button-solid" href="/productoverview">Explore Our Collection</a>
        </div>
    </div>
</section>
<style>/*-- -------------------------- -->
<---       Why Choose Us        -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #why-choose-682 {
        padding: var(--sectionPadding);
    }
    #why-choose-682 .cs-container {
        width: 100%;
        max-width: 80rem;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        /* 48px - 64px */
        gap: clamp(3rem, 6vw, 4rem);
    }
    #why-choose-682 .cs-content {
        /* set text align to left if content needs to be left aligned */
        text-align: left;
        width: 100%;
        max-width: 33.875rem;
        display: flex;
        flex-direction: column;
        /* centers content horizontally, set to flex-start to left align */
        align-items: flex-start;
    }
    #why-choose-682 .cs-topper {
        font-family: 'Cormorant Garamond', serif;
        font-size: var(--topperFontSize);
        line-height: 1.2em;
        text-transform: uppercase;
        text-align: inherit;
        letter-spacing: 0.15em;
        font-weight: 600;
        color: var(--primary);
        margin-bottom: 0.25rem;
        display: block;
        font-style: italic;
    }
    #why-choose-682 .cs-title {
        font-family: 'Playfair Display', serif;
        font-size: var(--headerFontSize);
        font-weight: 700;
        line-height: 1.1em;
        text-align: inherit;
        max-width: 43.75rem;
        margin: 0 0 1rem 0;
        color: var(--headerColor);
        position: relative;
        letter-spacing: -0.02em;
    }
    #why-choose-682 .cs-text {
        font-size: var(--bodyFontSize);
        line-height: 1.5em;
        text-align: inherit;
        width: 100%;
        max-width: 40.625rem;
        margin: 0;
        color: var(--bodyTextColor);
    }
    #why-choose-682 .cs-color {
        color: var(--primary);
    }
    #why-choose-682 .cs-text {
        margin-bottom: 1rem;
    }
    #why-choose-682 .cs-text:last-of-type {
        margin-bottom: 2rem;
    }
    #why-choose-682 .cs-ul {
        width: 100%;
        margin: 0 0 2rem 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }
    #why-choose-682 .cs-li {
        list-style: none;
        margin: 0;
        padding: 0;
    }
    #why-choose-682 .cs-h3 {
        font-family: 'Playfair Display', serif;
        font-size: 1.25rem;
        line-height: 1.2em;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
        color: var(--headerColor);
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        gap: 0.75rem;
        letter-spacing: -0.01em;
    }
    #why-choose-682 .cs-icon {
        width: 1.0625rem;
        height: auto;
        margin-top: 0.25rem;
    }
    #why-choose-682 .cs-item-text {
        font-size: 0.875rem;
        line-height: 1.5em;
        text-align: left;
        margin: 0;
        color: var(--bodyTextColor);
    }
    #why-choose-682 .cs-button-solid {
        font-family: 'Poppins', sans-serif;
        font-size: 1rem;
        /* 46px - 56px */
        line-height: clamp(2.875rem, 5.5vw, 3.5rem);
        text-decoration: none;
        font-weight: 600;
        text-align: center;
        margin: 0;
        color: #fff;
        min-width: 9.375rem;
        padding: 0 1.5rem;
        background-color: var(--primary);
        border-radius: 0.25rem;
        display: inline-block;
        letter-spacing: 0.02em;
        position: relative;
        z-index: 1;
        /* prevents padding from adding to the width */
        box-sizing: border-box;
    }
    #why-choose-682 .cs-button-solid:before {
        content: "";
        position: absolute;
        height: 100%;
        width: 0%;
        background: #E67E5B; /* Darker peach for hover */
        opacity: 1;
        top: 0;
        left: 0;
        z-index: -1;
        border-radius: 0.25rem;
        transition: width 0.3s;
    }
    #why-choose-682 .cs-button-solid:hover:before {
        width: 100%;
    }
    #why-choose-682 .cs-image-group {
        /* everything inside the image group is in ems so the font size will scale them down.  Font size minimum is tied to view width size, and grows until it reaches 75% of the value on em */
        font-size: min(2.3vw, 0.75em);
        width: 39.5em;
        height: 51.25em;
        position: relative;
        z-index: 1;
        /* flips it horizontally */
        transform: scaleX(-1);
    }
    #why-choose-682 .cs-picture {
        width: 19.125em;
        height: 22.5em;
        overflow: hidden;
        display: block;
        position: absolute;
    }
    #why-choose-682 .cs-picture img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        /* so the images are the original orientation, undoing the flip the cs-image-group is doing */
        transform: scaleX(-1);
    }
    #why-choose-682 .cs-picture1 {
        top: 5em;
        left: 0;
    }
    #why-choose-682 .cs-picture2 {
        top: 0;
        right: 0;
    }
    #why-choose-682 .cs-picture3 {
        bottom: 0;
        left: 0;
    }
    #why-choose-682 .cs-picture4 {
        bottom: 5em;
        right: 0;
    }
    #why-choose-682 .cs-graphic {
        display: none;
    }
}
/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #why-choose-682 .cs-container {
        flex-direction: row;
        justify-content: space-between;
    }
    #why-choose-682 .cs-image-group {
        font-size: min(1.2vw, 1em);
        flex: none;
        /* sends it to the right in the 2nd position */
        order: 2;
    }
    #why-choose-682 .cs-graphic {
        width: 61.4375em;
        height: auto;
        display: block;
        position: absolute;
        bottom: 10.3125em;
        right: -2em;
        z-index: -1;
    }
}

</style>        